import { Component, Input, OnInit, OnDestroy, Output, EventEmitter, ViewChild, ElementRef, AfterViewInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators, FormControl, AbstractControl } from "@angular/forms";
import { MetadataService } from '../services/metadata.service'
import { HttpClient } from "@angular/common/http";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { environment } from '../../environments/environment';
import { TableUtilsService } from '../services/table-utils.service';

// Component imports
import { InitialInputComponent } from './components/initial-input/initial-input.component';
import { FormHeaderComponent } from './components/form-header/form-header.component';
import { RegularFieldComponent } from './components/regular-field/regular-field.component';
import { FormActionsComponent } from './components/form-actions/form-actions.component';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-dynamic-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InitialInputComponent,
    FormHeaderComponent,
    RegularFieldComponent,
    FormActionsComponent,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTooltipModule,
    MatExpansionModule,
    MatTableModule,
    MatChipsModule,
    MatAutocompleteModule
  ],
  templateUrl: './dynamic-form.component.html',
  styleUrl: './dynamic-form.component.scss'
})
export class DynamicFormComponent implements OnInit, OnDestroy, AfterViewInit {
  columnCount = 1;
  columns: any[][] = [];
  @Input() tableName!: string;
  @Input() screenName!: string; // Add support for screen name
  @Input() data: any;
  @Output() dataChange = new EventEmitter<any>();
  @ViewChild('mySelect') mySelect!: ElementRef;
  @ViewChild('formActions') formActions!: FormActionsComponent;

  form!: FormGroup;
  fields: any[] = [];
  submissionSuccess = false;
  errorMessage = "";
  isLoading = false;
  showInitialInput = true;
  isViewMode = false;
  isAuth = true;
  successMessage: string = "";
  showSuccessPopup = false;
  showValidation: boolean = false;
  validationResult: any;
  isTenantBasedFlag: boolean = false;
  authorizeNumber = 1;
  isRowView: boolean = false; // Toggle between nested view and row view

  // SubScreen properties
  hasSubScreens: boolean = false;
  subScreens: string[] = [];
  subScreensMetadata: any[] = [];
  subScreenForms: { [key: string]: FormGroup } = {};
  subScreenColumns: { [key: string]: any[][] } = {};
  selectedTabIndex: number = 0;

  private metadataService = inject(MetadataService);
  private fb = inject(FormBuilder);
  private http = inject(HttpClient);
  private tableUtilsService = inject(TableUtilsService);
  private cdRef = inject(ChangeDetectorRef);

  constructor() { }

  ngOnInit() {
    if (this.tableName || this.screenName) {
      this.initializeForm();
    }
  }

  ngOnDestroy() {
    // Cleanup handled by individual components
  }

  ngAfterViewInit() {
    // Dropdown functionality is now handled by the unified DropdownComponent
  }

  initializeForm() {
    this.form = this.fb.group({
      ID: ["", Validators.required],
    });
  }

  setFormReadonly(isReadonly: boolean) {
    const disableControls = (control: AbstractControl) => {
      if (control instanceof FormGroup) {
        Object.values(control.controls).forEach(disableControls);
      } else if (control instanceof FormArray) {
        control.controls.forEach(disableControls);
      } else {
        if (isReadonly) {
          control.disable({ emitEvent: false });
        } else {
          control.enable({ emitEvent: false });
        }
      }
    };

    disableControls(this.form); // Apply disable logic to the entire form

    // Explicitly handle isMulti fields
    this.fields.forEach((field) => {
      if (field.isMulti) {
        const formArray = this.form.get(field.fieldName) as FormArray;
        if (formArray) {
          formArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }

      // Explicitly handle grouped fields
      if (field.Group) {
        const groupArray = this.getGroupArray(field.Group);
        if (groupArray) {
          groupArray.controls.forEach((control) => {
            if (isReadonly) {
              control.disable({ emitEvent: false });
            } else {
              control.enable({ emitEvent: false });
            }
          });
        }
      }
    });
  }

  viewData() {
    console.log('🔍 VIEW: viewData called');
    const idValue = this.form.get('ID')?.value;
    console.log('🔍 VIEW: ID value:', idValue);
    if (!idValue || idValue.trim() === '') {
      console.log('🔍 VIEW: No ID value, showing validation');
      this.showValidation = true;
      return;
    }

    console.log('🔍 VIEW: Setting view mode and loading data');
    this.isViewMode = true;
    this.loadDataAndBuildForm();
    setTimeout(() => {
      this.setFormReadonly(true);
    }, 0);
  }

  loadDataAndBuildForm() {
    console.log('🔍 LOAD: loadDataAndBuildForm called');
    const idValue = this.form.get('ID')?.value;
    console.log('🔍 LOAD: ID value:', idValue);
    if (!idValue || idValue.trim() === '') {
      console.log('🔍 LOAD: No ID value, showing validation');
      this.showValidation = true;
      return;
    }

    this.isLoading = true;
    this.errorMessage = "";
    this.successMessage = "";
    const id = this.form.get("ID")?.value;
    const tableNameToUse = this.tableName || this.screenName;
    const tableNameForValidation = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    const apiUrl = `${environment.baseURL}/api/validation/validate-id?tableName=${tableNameForValidation}&id=${id}`;
    console.log('🔍 LOAD: Validation API URL:', apiUrl);

    this.http.get(apiUrl, { withCredentials: true }).subscribe({
      next: (response: any) => {
        console.log('🔍 LOAD: Validation response:', response);
        if (response.success) {
          console.log('🔍 LOAD: Validation successful, loading table metadata');
          this.showInitialInput = false;
          this.loadTableMetadata();
        } else {
          console.log('🔍 LOAD: Validation failed:', response.message);
          this.errorMessage = response.message || "ID validation failed";
          this.isLoading = false;
        }
      },
      error: (error) => {
        console.error('🔍 LOAD: Validation error:', error);
        this.errorMessage = "Error validating ID";
        this.isLoading = false;
      },
      complete: () => {
        console.log('🔍 LOAD: Validation call completed');
        this.isLoading = false;
      }
    });
  }

  loadTableMetadata() {
    console.log('🔍 METADATA: loadTableMetadata called');
    this.isLoading = true;
    const metadataObservable = this.screenName
      ? this.metadataService.getScreenMetadata(this.screenName)
      : this.metadataService.getTableMetadata(this.tableName);

    console.log('🔍 METADATA: Using screenName:', this.screenName, 'tableName:', this.tableName);

    metadataObservable.subscribe({
      next: (response: any) => {
        console.log('🔍 METADATA: Metadata response:', response);
        if (response?.data?.fieldName) {
          console.log('🔍 METADATA: Processing metadata with fields:', response.data.fieldName.length);

          // ✅ Get columnNumber or default to 1
          this.columnCount = response.data.columnNumber || 1;

          // ✅ Order fields as usual
          const orderedFields = this.orderFieldsBasedOnFormDefinition(response.data.fieldName);
          // ⛔️ Skip "ID" so it doesn't take a layout slot
          const visibleFields = orderedFields.filter(
            field => field.fieldName?.toUpperCase() !== 'ID'
          );
          // ✅ Store isTenantBased if present
          if (response.data.isTenantBased) {
            this.isTenantBasedFlag = response.data.isTenantBased;
          }

          // ✅ Split fields into columns
          this.columns = this.distributeFieldsRoundRobin(visibleFields, this.columnCount);

          // ✅ Still keep old field assignment if you rely on it elsewhere
          this.fields = orderedFields;
          console.log('🔍 METADATA: Fields assigned:', this.fields.map(f => f.fieldName));

          // ========================================
          // SUBSCREEN PROCESSING
          // ========================================
          this.processSubScreens(response.data);

          console.log('🔍 METADATA: Building form');
          this.buildForm();
          console.log('🔍 METADATA: Form built, fetching data');
          this.fetchFormData();
          // Handle defaultFields if present in metadata response
          if (response.data.defaultFields && Array.isArray(response.data.defaultFields)) {
            this.populateDefaultFields(response.data.defaultFields);
          }
        } else {
          console.log('🔍 METADATA: No fieldName in response');
          this.isLoading = false;
        }
      },
      error: (error) => {
        console.error('🔍 METADATA: Error loading metadata:', error);
        this.isLoading = false;
      },
      complete: () => {
        console.log('🔍 METADATA: Metadata loading completed');
        this.isLoading = false;
      },
    });
  }
  private distributeFieldsRoundRobin(fields: any[], columnCount: number): any[][] {
    const columns: any[][] = Array.from({ length: columnCount }, () => []);
    fields.forEach((field, index) => {
      const colIndex = index % columnCount;
      columns[colIndex].push(field);
    });
    return columns;
  }


  /**
   * Orders fields based on the formDefinition response structure
   * Fields with Group "fieldName" appear first, then other fields in their original order
   */
  private orderFieldsBasedOnFormDefinition(fields: any[]): any[] {
    if (!fields || !Array.isArray(fields)) {
      return fields;
    }

    // Separate fields with Group "fieldName" and preserve their order
    const fieldNameGroupFields = fields.filter(field => field.Group === "fieldName");

    // Get all other fields (non-fieldName group) and preserve their order
    const otherFields = fields.filter(field => field.Group !== "fieldName");

    // Combine: fieldName group fields first, then other fields in their original order
    return [
      ...fieldNameGroupFields,
      ...otherFields
    ];
  }

  /**
   * Processes SubScreen data from metadata response
   * @param data - The metadata response data
   */
  processSubScreens(data: any) {
    if (data.subScreen && Array.isArray(data.subScreen) && data.subScreen.length > 0) {
      this.hasSubScreens = true;
      this.subScreens = data.subScreen;
      this.subScreensMetadata = data.subScreensMetadata || [];

      // Process each SubScreen
      this.subScreensMetadata.forEach((subScreenMetadata: any) => {
        this.processSubScreen(subScreenMetadata);
      });
    } else {
      this.hasSubScreens = false;
      this.subScreens = [];
      this.subScreensMetadata = [];
    }
  }

  /**
   * Processes a single SubScreen metadata
   * @param subScreenMetadata - The SubScreen metadata
   */
  processSubScreen(subScreenMetadata: any) {
    const subScreenId = subScreenMetadata.ID;
    const columnCount = subScreenMetadata.columnNumber || 1;

    console.log(`Processing SubScreen: ${subScreenId}`, subScreenMetadata);

    // Order fields using existing method
    const orderedFields = this.orderFieldsBasedOnFormDefinition(subScreenMetadata.fieldName || []);

    // Filter out ID fields and other non-visible fields
    const visibleFields = orderedFields.filter((field: any) =>
      field.fieldName?.toUpperCase() !== 'ID'
    );

    console.log(`SubScreen ${subScreenId} visible fields:`, visibleFields);

    // Distribute fields into columns for this SubScreen
    this.subScreenColumns[subScreenId] = this.distributeFieldsRoundRobin(visibleFields, columnCount);

    console.log(`SubScreen ${subScreenId} columns:`, this.subScreenColumns[subScreenId]);

    // Create form group for this SubScreen
    this.subScreenForms[subScreenId] = this.createSubScreenForm(subScreenMetadata);

    console.log(`SubScreen ${subScreenId} form:`, this.subScreenForms[subScreenId]);

    // Debug form controls
    this.debugSubScreenFormControls(subScreenId);
  }

  /**
   * Debug method to log subscreen form controls
   * @param subScreenId - The SubScreen ID
   */
  debugSubScreenFormControls(subScreenId: string) {
    const form = this.subScreenForms[subScreenId];
    if (form) {
      console.log(`SubScreen ${subScreenId} form controls:`, Object.keys(form.controls));

      // Check grouped fields
      Object.keys(form.controls).forEach(controlName => {
        const control = form.get(controlName);
        if (control instanceof FormArray) {
          console.log(`SubScreen ${subScreenId} group ${controlName}:`, control);
          control.controls.forEach((groupControl, index) => {
            if (groupControl instanceof FormGroup) {
              console.log(`SubScreen ${subScreenId} group ${controlName}[${index}] controls:`, Object.keys(groupControl.controls));
            }
          });
        }
      });
    }
  }

  /**
   * Creates a FormGroup for a SubScreen with support for groups and multi-fields
   * @param subScreenMetadata - The SubScreen metadata
   * @returns FormGroup for the SubScreen
   */
  private createSubScreenForm(subScreenMetadata: any): FormGroup {
    const formGroup = this.fb.group({});
    const groupedFields: { [key: string]: FormArray } = {};

    // Add ID field
    formGroup.addControl('ID', this.fb.control(''));

    if (subScreenMetadata.fieldName && Array.isArray(subScreenMetadata.fieldName)) {
      // Process each field
      subScreenMetadata.fieldName.forEach((field: any) => {
        if (field.fieldName !== "ID") {
          if (field.isMulti && !field.Group) {
            // Non-grouped multi-field
            const multiFieldArray = this.fb.array([this.createMultiField(field)]);
            formGroup.addControl(field.fieldName, multiFieldArray);

            // Disable multi-field if noInput is true
            if (field.noInput) {
              multiFieldArray.disable({ emitEvent: false });
            }
          } else if (field.Group) {
            const parsed = this.parseGroupPath(field.Group);
            if (!parsed.isNested) {
              // Direct field of parent group - already handled in addSubScreenGroup
            } else {
              // Nested group field - already handled in addSubScreenGroup
            }
          } else {
            // Regular field
            const validators = field.mandatory ? Validators.required : null;
            let control;
            switch (field.type) {
              case "boolean":
                control = this.fb.control(false, validators);
                break;
              case "date":
                control = this.fb.control(null, validators);
                break;
              default:
                control = this.fb.control("", validators);
                break;
            }
            formGroup.addControl(field.fieldName, control);

            // Disable control if noInput is true
            if (field.noInput) {
              control.disable({ emitEvent: false });
            }
          }
        }
      });

      // Handle grouped fields
      const groupNames = [...new Set(
        subScreenMetadata.fieldName
          .filter((field: any) => field.Group)
          .map((field: any) => this.parseGroupPath(field.Group).parent)
          .filter((name: any) => name) // Filter out null/undefined values
      )] as string[];

      groupNames.forEach((groupName: string) => {
        if (groupName && !groupedFields[groupName]) {
          groupedFields[groupName] = this.fb.array([this.createSubScreenGroup(subScreenMetadata, groupName)]);
          formGroup.addControl(groupName, groupedFields[groupName]);
        }
      });
    }

    return formGroup;
  }

  /**
   * Creates a FormGroup for a subscreen group with subscreen-specific fields
   * @param subScreenMetadata - The SubScreen metadata
   * @param groupName - The group name
   * @returns FormGroup for the subscreen group
   */
  createSubScreenGroup(subScreenMetadata: any, groupName: string): FormGroup {
    const group = this.fb.group({});

    // Handle nested groups
    const parsed = this.parseGroupPath(groupName);
    if (parsed.isNested && parsed.parent && parsed.child) {
      // This is a nested group, create fields for this specific path
      this.getSubScreenFieldsForGroupPath(subScreenMetadata.ID, groupName).forEach((field: any) => {
        this.addFieldToGroup(group, field);
      });
    } else {
      // This is a parent group, create fields and nested subgroups
      this.getSubScreenFieldsForGroup(subScreenMetadata.ID, groupName).forEach((field: any) => {
        const fieldParsed = this.parseGroupPath(field.Group);
        if (!fieldParsed.isNested) {
          // Direct field of this group
          this.addFieldToGroup(group, field);
        }
      });

      // Add nested subgroups
      const childGroups = this.getSubScreenChildGroups(subScreenMetadata, groupName);
      childGroups.forEach(childGroup => {
        const childGroupArray = this.fb.array([this.createSubScreenGroup(subScreenMetadata, `${groupName}|${childGroup}`)]);
        group.addControl(childGroup, childGroupArray);
      });
    }

    return group;
  }

  /**
   * Get child groups for a parent group in a subscreen
   * @param subScreenMetadata - The SubScreen metadata
   * @param parentGroup - The parent group name
   * @returns Array of child group names
   */
  getSubScreenChildGroups(subScreenMetadata: any, parentGroup: string): string[] {
    const childGroups = new Set<string>();
    if (subScreenMetadata.fieldName) {
      subScreenMetadata.fieldName.forEach((field: any) => {
        if (field.Group) {
          const parsed = this.parseGroupPath(field.Group);
          if (parsed.parent === parentGroup.trim() && parsed.child) {
            childGroups.add(parsed.child);
          }
        }
      });
    }
    return Array.from(childGroups);
  }

  /**
   * Tab selection method
   * @param index - The index of the selected tab
   */
  selectTab(index: number) {
    this.selectedTabIndex = index;
  }

  /**
   * Get SubScreen form by ID
   * @param subScreenId - The SubScreen ID
   * @returns FormGroup for the SubScreen
   */
  getSubScreenForm(subScreenId: string): FormGroup {
    return this.subScreenForms[subScreenId] || this.fb.group({});
  }

  /**
   * Get SubScreen columns by ID
   * @param subScreenId - The SubScreen ID
   * @returns Array of field columns
   */
  getSubScreenColumns(subScreenId: string): any[][] {
    return this.subScreenColumns[subScreenId] || [];
  }

  /**
   * Get SubScreen column count by ID
   * @param subScreenId - The SubScreen ID
   * @returns Number of columns
   */
  getSubScreenColumnCount(subScreenId: string): number {
    const metadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    return metadata?.columnNumber || 1;
  }

  /**
   * Get multi-array from subscreen form
   * @param subScreenId - The SubScreen ID
   * @param fieldName - The field name
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   * @returns FormArray for the multi-field in subscreen
   */
  getSubScreenMultiArray(subScreenId: string, fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    const subScreenForm = this.subScreenForms[subScreenId];
    if (!subScreenForm) {
      return this.fb.array([]);
    }

    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = subScreenForm.get(parsed.parent) as FormArray;
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = subScreenForm.get(groupName) as FormArray;
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return subScreenForm.get(fieldName) as FormArray;
    }
  }

  /**
   * Add multi-field to subscreen
   * @param subScreenId - The SubScreen ID
   * @param field - The field definition
   * @param groupIndex - Index of the group (optional)
   * @param index - Index to insert at (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  addSubScreenMultiField(subScreenId: string, field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number) {
    try {
      const multiArray = this.getSubScreenMultiArray(subScreenId, field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }
    } catch (error) {
      console.error('Error adding subscreen multi-field:', error);
    }
  }

  /**
   * Remove multi-field from subscreen
   * @param subScreenId - The SubScreen ID
   * @param fieldName - The field name
   * @param index - Index to remove
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  removeSubScreenMultiField(subScreenId: string, fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) {
    const multiArray = this.getSubScreenMultiArray(subScreenId, fieldName, groupIndex, groupName, nestedGroupIndex);
    multiArray.removeAt(index);
  }

  /**
   * Get group array from subscreen form
   * @param subScreenId - The SubScreen ID
   * @param groupName - The group name
   * @returns FormArray for the group in subscreen
   */
  getSubScreenGroupArray(subScreenId: string, groupName: string): FormArray {
    const subScreenForm = this.subScreenForms[subScreenId];
    if (!subScreenForm) {
      return this.fb.array([]);
    }
    return subScreenForm.get(groupName) as FormArray;
  }

  /**
   * Gets all fields that belong to a specific group in a subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupName - Name of the group
   * @returns Array of fields belonging to this group in the subscreen
   */
  getSubScreenFieldsForGroup(subScreenId: string, groupName: string) {
    const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    if (!subScreenMetadata || !subScreenMetadata.fieldName) {
      return [];
    }
    return subScreenMetadata.fieldName.filter((field: any) => field.Group && field.Group.trim() === groupName.trim());
  }

  /**
   * Check if this is the first field in a parent group for a subscreen (for rendering group headers)
   * @param subScreenId - The SubScreen ID
   * @param field - The field to check
   * @returns True if this is the first field in the parent group
   */
  isFirstFieldInParentGroupForSubScreen(subScreenId: string, field: any): boolean {
    if (!field.Group) return false;

    const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    if (!subScreenMetadata || !subScreenMetadata.fieldName) {
      return false;
    }

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.parent) return false;

    // Find the first field that belongs to this parent group in the subscreen
    const firstFieldIndex = subScreenMetadata.fieldName.findIndex((f: any) => {
      if (!f.Group) return false;
      const fParsed = this.parseGroupPath(f.Group);
      return fParsed.parent === parsed.parent;
    });

    return firstFieldIndex === subScreenMetadata.fieldName.indexOf(field);
  }

  /**
   * Get fields for a specific group path in a subscreen (supports nested groups with pipe notation)
   * @param subScreenId - The SubScreen ID
   * @param groupPath - The full group path (e.g., "type|field")
   * @returns Array of fields belonging to this group path in the subscreen
   */
  getSubScreenFieldsForGroupPath(subScreenId: string, groupPath: string) {
    const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    if (!subScreenMetadata || !subScreenMetadata.fieldName) {
      return [];
    }
    return subScreenMetadata.fieldName.filter((field: any) => field.Group && field.Group.trim() === groupPath.trim());
  }

  /**
   * Get nested group array from subscreen using path notation
   * @param subScreenId - The SubScreen ID
   * @param groupPath - Path like "type|field" for nested groups
   * @param parentIndex - Index of parent group instance
   */
  getSubScreenNestedGroupArray(subScreenId: string, groupPath: string, parentIndex?: number): FormArray {
    const subScreenForm = this.subScreenForms[subScreenId];
    if (!subScreenForm) {
      return this.fb.array([]);
    }

    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child && parentIndex !== undefined) {
      const parentArray = subScreenForm.get(parsed.parent) as FormArray;
      const parentGroup = parentArray.at(parentIndex) as FormGroup;
      return parentGroup.get(parsed.child) as FormArray;
    }
    return subScreenForm.get(groupPath) as FormArray;
  }

  /**
   * Add group to subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupName - The group name
   * @param index - Index to insert at (optional)
   */
  addSubScreenGroup(subScreenId: string, groupName: string, index?: number) {
    const groupArray = this.getSubScreenGroupArray(subScreenId, groupName);
    const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    const newGroup = subScreenMetadata ? this.createSubScreenGroup(subScreenMetadata, groupName) : this.createGroup(groupName);

    if (index !== undefined) {
      groupArray.insert(index + 1, newGroup);
    } else {
      groupArray.push(newGroup);
    }
  }

  /**
   * Remove group from subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupName - The group name
   * @param index - Index to remove
   */
  removeSubScreenGroup(subScreenId: string, groupName: string, index: number) {
    this.getSubScreenGroupArray(subScreenId, groupName).removeAt(index);
  }

  /**
   * Clone group in subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupName - The group name
   * @param index - Index to clone
   */
  cloneSubScreenGroup(subScreenId: string, groupName: string, index: number): void {
    const groupArray = this.getSubScreenGroupArray(subScreenId, groupName);
    const groupToClone = groupArray.at(index) as FormGroup;

    // Step 1: Add a new empty group using your existing method
    this.addSubScreenGroup(subScreenId, groupName, index);

    // Step 2: Get the newly created group (it's at index + 1)
    const newGroup = groupArray.at(index + 1) as FormGroup;

    // Step 3: Clone all values from the original group to the new group
    Object.keys(groupToClone.controls).forEach(key => {
      const originalControl = groupToClone.get(key);
      const newControl = newGroup.get(key);

      if (originalControl instanceof FormArray && newControl instanceof FormArray) {
        // Clear the default entry and copy all entries from original
        newControl.clear();
        originalControl.controls.forEach(control => {
          if (control instanceof FormGroup) {
            const newSubControl = this.fb.group({});
            Object.keys(control.controls).forEach(subKey => {
              newSubControl.addControl(subKey, this.fb.control(control.get(subKey)?.value));
            });
            newControl.push(newSubControl);
          }
        });
      } else {
        newControl?.setValue(originalControl?.value);
      }
    });
  }

  /**
   * Add nested group to subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to insert at (optional)
   */
  addSubScreenNestedGroup(subScreenId: string, groupPath: string, parentIndex: number, index?: number) {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const nestedArray = this.getSubScreenNestedGroupArray(subScreenId, groupPath, parentIndex);
      const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
      const newGroup = subScreenMetadata ? this.createSubScreenGroup(subScreenMetadata, groupPath) : this.createGroup(groupPath);

      if (index !== undefined) {
        nestedArray.insert(index + 1, newGroup);
      } else {
        nestedArray.push(newGroup);
      }
    }
  }

  /**
   * Remove nested group from subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to remove
   */
  removeSubScreenNestedGroup(subScreenId: string, groupPath: string, parentIndex: number, index: number) {
    this.getSubScreenNestedGroupArray(subScreenId, groupPath, parentIndex).removeAt(index);
  }

  /**
   * Clone nested group in subscreen
   * @param subScreenId - The SubScreen ID
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to clone
   */
  cloneSubScreenNestedGroup(subScreenId: string, groupPath: string, parentIndex: number, index: number): void {
    const nestedArray = this.getSubScreenNestedGroupArray(subScreenId, groupPath, parentIndex);
    const groupToClone = nestedArray.at(index) as FormGroup;

    // Add new group
    this.addSubScreenNestedGroup(subScreenId, groupPath, parentIndex, index);

    // Get the newly created group
    const newGroup = nestedArray.at(index + 1) as FormGroup;

    // Clone values
    Object.keys(groupToClone.controls).forEach(key => {
      const originalControl = groupToClone.get(key);
      const newControl = newGroup.get(key);

      if (originalControl instanceof FormArray && newControl instanceof FormArray) {
        newControl.clear();
        originalControl.controls.forEach(control => {
          if (control instanceof FormGroup) {
            const newSubControl = this.fb.group({});
            Object.keys(control.controls).forEach(subKey => {
              newSubControl.addControl(subKey, this.fb.control(control.get(subKey)?.value));
            });
            newControl.push(newSubControl);
          }
        });
      } else {
        newControl?.setValue(originalControl?.value);
      }
    });
  }

  /**
   * Debug method to log subscreen field information
   * @param subScreenId - The SubScreen ID
   */
  debugSubScreenFields(subScreenId: string) {
    const metadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
    if (metadata) {
      console.log(`SubScreen ${subScreenId} fields:`, metadata.fieldName);
      console.log(`SubScreen ${subScreenId} columns:`, this.subScreenColumns[subScreenId]);
      console.log(`SubScreen ${subScreenId} form:`, this.subScreenForms[subScreenId]);
    }
  }

  buildForm() {
    console.log('🔍 BUILD: buildForm called');
    const groupedFields: { [key: string]: FormArray } = {};

    // First, create all parent groups
    const parentGroups = this.getParentGroups();
    console.log('🔍 BUILD: Parent groups:', parentGroups);
    parentGroups.forEach(parentGroup => {
      if (!groupedFields[parentGroup]) {
        groupedFields[parentGroup] = this.fb.array([]);
        this.form.addControl(parentGroup, groupedFields[parentGroup]);
        this.addGroup(parentGroup);
      }
    });

    this.fields.forEach((field) => {
      if (field.fieldName !== "ID") {
        if (field.isMulti && !field.Group) {
          // Non-grouped multi-field
          const multiFieldArray = this.fb.array([this.createMultiField(field)]);
          this.form.addControl(field.fieldName, multiFieldArray);

          // Disable multi-field if noInput is true
          if (field.noInput) {
            multiFieldArray.disable({ emitEvent: false });
          }
        } else if (field.Group) {
          const parsed = this.parseGroupPath(field.Group);
          if (!parsed.isNested) {
            // Direct field of parent group - already handled in createGroup
          } else {
            // Nested group field - already handled in createGroup
          }
        } else {
          // Non-grouped regular field
          const validators = field.mandatory ? Validators.required : null;
          let control;
          switch (field.type) {
            case "boolean":
              control = this.fb.control(false, validators);
              break;
            case "date":
              control = this.fb.control(null, validators);
              break;
            default:
              control = this.fb.control("", validators);
              break;
          }
          this.form.addControl(field.fieldName, control);

          // Disable control if noInput is true
          if (field.noInput) {
            control.disable({ emitEvent: false });
          }


        }
      }
    });

    // Add SubScreen forms to the main form
    if (this.hasSubScreens) {
      this.subScreens.forEach((subScreenId: string) => {
        if (this.subScreenForms[subScreenId]) {
          this.form.addControl(subScreenId, this.subScreenForms[subScreenId]);
        }
      });
    }
    
    console.log('🔍 BUILD: Form built successfully');
    console.log('🔍 BUILD: Form controls:', Object.keys(this.form.controls));
  }




  createGroup(groupName: string): FormGroup {
    const group = this.fb.group({});

    // Handle nested groups
    const parsed = this.parseGroupPath(groupName);
    if (parsed.isNested && parsed.parent && parsed.child) {
      // This is a nested group, create fields for this specific path
      this.getFieldsForGroupPath(groupName).forEach((field) => {
        this.addFieldToGroup(group, field);
      });
    } else {
      // This is a parent group, create fields and nested subgroups
      this.getFieldsForGroup(groupName).forEach((field) => {
        const fieldParsed = this.parseGroupPath(field.Group);
        if (!fieldParsed.isNested) {
          // Direct field of this group
          this.addFieldToGroup(group, field);
        }
      });

      // Add nested subgroups
      const childGroups = this.getChildGroups(groupName);
      childGroups.forEach(childGroup => {
        const childGroupArray = this.fb.array([this.createGroup(`${groupName}|${childGroup}`)]);
        group.addControl(childGroup, childGroupArray);
      });
    }

    return group;
  }

  /**
   * Helper method to add a field to a FormGroup
   */
  private addFieldToGroup(group: FormGroup, field: any): void {
    if (field.isMulti) {
      const multiFieldArray = this.fb.array([this.createMultiField(field)]);
      group.addControl(field.fieldName, multiFieldArray);

      // Disable multi-field if noInput is true
      if (field.noInput) {
        multiFieldArray.disable({ emitEvent: false });
      }
    } else if (field.foreginKey) {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }


    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
  }

  // ========================================
  // MULTI FIELD COMPONENT SECTION
  // ========================================

  /**
   * Creates a FormGroup for multi-field components
   * @param field - Field configuration object
   * @returns FormGroup for the multi-field
   */
  createMultiField(field: any): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(field)) {
      field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", field.mandatory ? Validators.required : null);
      group.addControl(field.fieldName, control);

      // Disable control if noInput is true
      if (field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  /**
   * Gets the FormArray for a multi-field
   * @param fieldName - Name of the field
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   * @returns FormArray for the multi-field
   */
  getMultiArray(fieldName: string, groupIndex?: number, groupName?: string, nestedGroupIndex?: number): FormArray {
    if (groupIndex !== undefined && groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(groupName);
      if (parsed.isNested && parsed.parent && parsed.child && nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = this.getGroupArray(parsed.parent);
        const parentGroup = parentArray.at(groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(nestedGroupIndex) as FormGroup;
        return childGroup.get(fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = this.getGroupArray(groupName);
        const group = groupArray.at(groupIndex) as FormGroup;
        return group.get(fieldName) as FormArray;
      }
    } else {
      return this.form.get(fieldName) as FormArray;
    }
  }

  /**
   * Adds a new multi-field instance
   * @param field - Field configuration
   * @param groupIndex - Index of the group (optional)
   * @param index - Index to insert at (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  addMultiField(field: any, groupIndex?: number, index?: number, groupName?: string, nestedGroupIndex?: number) {
    try {
      const multiArray = this.getMultiArray(field.fieldName, groupIndex, groupName, nestedGroupIndex);
      const newField = this.createMultiField(field);
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }

    } catch (error) {
      // Handle error silently
    }
  }

  /**
   * Removes a multi-field instance
   * @param fieldName - Name of the field
   * @param index - Index of the field to remove
   * @param groupIndex - Index of the group (optional)
   * @param groupName - Name of the group (optional)
   * @param nestedGroupIndex - Index of nested group (optional)
   */
  removeMultiField(fieldName: string, index: number, groupIndex?: number, groupName?: string, nestedGroupIndex?: number) {
    const multiArray = this.getMultiArray(fieldName, groupIndex, groupName, nestedGroupIndex);
    multiArray.removeAt(index);
  }

  /**
   * Creates a FormGroup for multi-field with metadata (used for data population)
   * @param fieldMeta - Field metadata
   * @param sampleData - Sample data for population (optional)
   * @returns FormGroup for the multi-field
   */
  createMultiField2(fieldMeta: any, sampleData?: any): FormGroup {
    const group = this.fb.group({});
    const control = this.fb.control(sampleData || "", fieldMeta.mandatory ? Validators.required : null);
    group.addControl(fieldMeta.fieldName, control);
    return group;
  }

  // ========================================
  // GROUPED FIELDS COMPONENT SECTION
  // ========================================

  /**
   * Gets all fields that belong to a specific group
   * @param groupName - Name of the group
   * @returns Array of fields belonging to this group
   */
  getFieldsForGroup(groupName: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupName.trim());
  }

  /**
   * Get fields for a specific group path (supports nested groups with pipe notation)
   * @param groupPath - The full group path (e.g., "type|field")
   * @returns Array of fields belonging to this group path
   */
  getFieldsForGroupPath(groupPath: string) {
    return this.fields.filter((field) => field.Group && field.Group.trim() === groupPath.trim());
  }

  /**
   * Parse group path to get parent and child group names
   * @param groupPath - The group path (e.g., "type|field")
   * @returns Object with parent and child group names
   */
  parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    // Trim the entire groupPath first to handle trailing spaces
    const trimmedGroupPath = groupPath.trim();

    if (trimmedGroupPath.includes('|')) {
      const parts = trimmedGroupPath.split('|');
      return {
        parent: parts[0].trim(),
        child: parts[1].trim(),
        isNested: true
      };
    }
    return {
      parent: trimmedGroupPath.trim(),
      child: null,
      isNested: false
    };
  }

  /**
   * Get all unique parent groups
   */
  getParentGroups(): string[] {
    const parentGroups = new Set<string>();
    this.fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent) {
          parentGroups.add(parsed.parent);
        }
      }
    });
    return Array.from(parentGroups);
  }

  /**
   * Get all child groups for a specific parent group
   */
  getChildGroups(parentGroup: string): string[] {
    const childGroups = new Set<string>();
    this.fields.forEach(field => {
      if (field.Group) {
        const parsed = this.parseGroupPath(field.Group);
        if (parsed.parent === parentGroup.trim() && parsed.child) {
          childGroups.add(parsed.child);
        }
      }
    });
    return Array.from(childGroups);
  }

  getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  /**
   * Get nested group array using path notation
   * @param groupPath - Path like "type|field" for nested groups
   * @param parentIndex - Index of parent group instance
   */
  getNestedGroupArray(groupPath: string, parentIndex?: number): FormArray {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child && parentIndex !== undefined) {
      const parentArray = this.getGroupArray(parsed.parent);
      const parentGroup = parentArray.at(parentIndex) as FormGroup;
      return parentGroup.get(parsed.child) as FormArray;
    }
    return this.getGroupArray(groupPath);
  }

  addGroup(groupName: string, index?: number) {
    const groupArray = this.getGroupArray(groupName);
    const newGroup = this.createGroup(groupName);

    if (index !== undefined) {
      groupArray.insert(index + 1, newGroup);
    } else {
      groupArray.push(newGroup);
    }
  }

  /**
   * Add nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index to insert at (optional)
   */
  addNestedGroup(groupPath: string, parentIndex: number, index?: number) {
    const parsed = this.parseGroupPath(groupPath);
    if (parsed.isNested && parsed.parent && parsed.child) {
      const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
      const newGroup = this.createGroup(groupPath);

      if (index !== undefined) {
        nestedArray.insert(index + 1, newGroup);
      } else {
        nestedArray.push(newGroup);
      }
    }
  }

  removeGroup(groupName: string, index: number) {
    this.getGroupArray(groupName).removeAt(index);
  }

  /**
   * Remove nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to remove
   */
  removeNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    nestedArray.removeAt(index);
  }

  /**
   * Clone nested group
   * @param groupPath - Path like "type|field"
   * @param parentIndex - Index of parent group instance
   * @param index - Index of nested group to clone
   */
  cloneNestedGroup(groupPath: string, parentIndex: number, index: number) {
    const nestedArray = this.getNestedGroupArray(groupPath, parentIndex);
    const groupToClone = nestedArray.at(index) as FormGroup;
    const clonedGroup = this.createGroup(groupPath);

    // Copy values from the original group to the cloned group
    Object.keys(groupToClone.controls).forEach(key => {
      const originalControl = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (originalControl && clonedControl) {
        if (originalControl instanceof FormArray) {
          // Handle FormArray cloning
          const originalArray = originalControl as FormArray;
          const clonedArray = clonedControl as FormArray;

          // Clear the default entry and copy all entries from original
          clonedArray.clear();
          originalArray.controls.forEach(control => {
            if (control instanceof FormGroup) {
              const newControl = this.fb.group({});
              Object.keys(control.controls).forEach(subKey => {
                newControl.addControl(subKey, this.fb.control(control.get(subKey)?.value));
              });
              clonedArray.push(newControl);
            }
          });
        } else {
          // Handle regular FormControl cloning
          clonedControl.setValue(originalControl.value);
        }
      }
    });

    nestedArray.insert(index + 1, clonedGroup);
  }



  isFirstFieldInGroup(field: any): boolean {
    return (
      this.fields.findIndex((f) => f.Group === field.Group) ===
      this.fields.indexOf(field)
    );
  }

  /**
   * Check if this is the first field in a parent group (for rendering group headers)
   */
  isFirstFieldInParentGroup(field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.parent) return false;

    // Find the first field that belongs to this parent group
    const firstFieldIndex = this.fields.findIndex((f) => {
      if (!f.Group) return false;
      const fParsed = this.parseGroupPath(f.Group);
      return fParsed.parent === parsed.parent;
    });

    return firstFieldIndex === this.fields.indexOf(field);
  }

  /**
   * Check if this is the first field in a nested group (for rendering nested group headers)
   */
  isFirstFieldInNestedGroup(field: any): boolean {
    if (!field.Group) return false;

    const parsed = this.parseGroupPath(field.Group);
    if (!parsed.isNested || !parsed.child) return false;

    // Find the first field that belongs to this specific nested group path
    return (
      this.fields.findIndex((f) => f.Group && f.Group.trim() === field.Group.trim()) ===
      this.fields.indexOf(field)
    );
  }

  trackByFieldName(_index: number, field: any): string {
    return field.fieldName;
  }












  // Helper method to extract part before comma for tables API calls
  // Note: extractTablesApiId moved to TableUtilsService

  fetchFormData() {
    console.log('🔍 FETCH: fetchFormData called');
    this.isLoading = true;
    const id = this.form.get("ID")?.value;
    console.log('🔍 FETCH: ID value:', id);
    
    // Use screenName if tableName is not available
    const tableNameToUse = this.tableName || this.screenName;
    console.log('🔍 FETCH: Table name to use:', tableNameToUse);
    
    const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    console.log('🔍 FETCH: Tables API ID:', tablesApiId);
    
    if (!tablesApiId) {
      console.error('❌ Error: No valid table name found for API call');
      this.errorMessage = "No valid table name found";
      this.isLoading = false;
      return;
    }
    
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}`;
    const params = {
      isTenantBased: this.isTenantBasedFlag.toString(),
    };
    console.log('🔍 FETCH: API URL:', apiUrl);
    console.log('🔍 FETCH: Params:', params);
    
    this.http.get(apiUrl, { withCredentials: true, params }).subscribe({
      next: (response: any) => {
        console.log('🔍 FETCH: API Response received:', response);
        if (response && response.data) {
          console.log('🔍 FETCH: Response data:', response.data);
          console.log('🔍 FETCH: recordStatus:', response.data.recordStatus);
          if(response.data.recordStatus)
          {
            console.log('🔍 FETCH: Setting isAuth to false');
            this.isAuth=false;
            // Remove the problematic detectChanges call
            // this.cdRef.detectChanges();
          }
          console.log('🔍 FETCH: Calling populateForm with data:', response.data);
          this.populateForm(response.data);
          // Also populate subscreen forms with the same data
          this.populateSubScreenForms(response.data);
        } else {
          console.log('🔍 FETCH: No response.data found');
        }
        // Handle defaultFields if present in response
        if (response && response.defaultFields && Array.isArray(response.defaultFields)) {
          this.populateDefaultFields(response.defaultFields);
        }
      },
      error: (error) => {
        console.error('🔍 FETCH: API Error:', error);
        this.errorMessage = "An error occurred while fetching data";
      },
      complete: () => {
        console.log('🔍 FETCH: API call completed');
        this.isLoading = false;
      }
    });
  }

  // populateForm(data: any) {
  //   Object.keys(data).forEach(key => {
  //     const formControl = this.form.get(key);

  //     if (formControl instanceof FormArray && Array.isArray(data[key])) {
  //       const formArray = formControl as FormArray;
  //       formArray.clear(); // Clear existing controls in the FormArray

  //       if (this.fields.some(field => field.Group === key)) {
  //         // Handle Group fields 
  //         data[key].forEach((groupData: any) => {
  //           formArray.push(this.createGroup(key)); 
  //           (formArray.at(formArray.length - 1) as FormGroup).patchValue(groupData);
  //         });
  //       } else {
  //         // Handle multi-fields 
  //         const field = this.fields.find(field => field.fieldName === key);
  //         if (field) {
  //           // Create form groups in the FormArray
  //           for (let i = 0; i < data[key].length; i++) {
  //             formArray.push(this.createMultiField(field));
  //           }

  //           // Patch the values
  //           data[key].forEach((value: any, index: number) => {
  //             const newGroup = formArray.at(index) as FormGroup;

  //             // Check if the value is an object (for multi-fields with multiple properties)
  //             if (typeof value === 'object' && !Array.isArray(value)) {  
  //               newGroup.patchValue(value); 
  //             } else {
  //               // If the value is not an object, patch it to the field.fieldName
  //               newGroup.patchValue({ [field.fieldName]: value }); 
  //             }
  //           });
  //         }
  //       }
  //     } else if (formControl) {
  //       // For simple fields (not FormArray)
  //       const field = this.fields.find(field => field.fieldName === key);
  //       if (field && field.type === 'date' && typeof data[key] === 'string') {
  //         const parsedDate = new Date(data[key]);
  //         const dateOnly = parsedDate.toISOString().split('T')[0];
  //         if (!isNaN(parsedDate.getTime())) {
  //           formControl.setValue(dateOnly);
  //         } else {
  //           formControl.setValue(null);
  //         }
  //       } else if (field && field.type === 'date' && Array.isArray(data[key])) {
  //         // Handle the case where data[key] is an array of date strings (for multi-fields)
  //         const parsedDates = data[key].map(dateStr => {
  //           const parsedDate = new Date(dateStr);
  //           return !isNaN(parsedDate.getTime()) ? parsedDate : null;
  //         });
  //         formControl.setValue(parsedDates);
  //       } else {
  //         formControl.setValue(data[key]);
  //       }

  //       if (this.isViewMode) {
  //         formControl.disable(); // Disable the control AFTER setting the value
  //       }
  //     }
  //   });
  // }

  populateForm(data: any): void {
    Object.keys(data).forEach(key => {
      // Find the field by matching trimmed field names
      const field = this.fields.find(field => field.fieldName.trim() === key.trim());
      const formControl = this.form.get(field?.fieldName || key);

      if (formControl instanceof FormArray && Array.isArray(data[key])) {
        const formArray = formControl as FormArray;
        formArray.clear(); // Clear existing controls in the FormArray

        // Check if this is a group field by matching trimmed names
        const isGroupField = this.fields.some(field => field.Group && field.Group.trim() === key.trim());
        if (isGroupField) {
          // Handle Group fields 
          data[key].forEach((groupData: any) => {
            formArray.push(this.createGroup(key)); 
            (formArray.at(formArray.length - 1) as FormGroup).patchValue(groupData);
          });
        } else {
          // Handle multi-fields 
          if (field) {
            // Create form groups in the FormArray
            for (let i = 0; i < data[key].length; i++) {
              formArray.push(this.createMultiField(field));
            }

            // Patch the values
            data[key].forEach((value: any, index: number) => {
              const newGroup = formArray.at(index) as FormGroup;

              // Check if the value is an object (for multi-fields with multiple properties)
              if (typeof value === 'object' && !Array.isArray(value)) {  
                newGroup.patchValue(value); 
              } else {
                // If the value is not an object, patch it to the field.fieldName
                newGroup.patchValue({ [field.fieldName]: value }); 
              }
            });
          }
        }
      } else if (formControl) {
        // For simple fields (not FormArray)
        if (field && field.type === 'date' && typeof data[key] === 'string') {
          const parsedDate = new Date(data[key]);
          const dateOnly = parsedDate.toISOString().split('T')[0];
          if (!isNaN(parsedDate.getTime())) {
            formControl.setValue(dateOnly);
          } else {
            formControl.setValue(null);
          }
        } else if (field && field.type === 'date' && Array.isArray(data[key])) {
          // Handle the case where data[key] is an array of date strings (for multi-fields)
          const parsedDates = data[key].map(dateStr => {
            const parsedDate = new Date(dateStr);
            return !isNaN(parsedDate.getTime()) ? parsedDate : null;
          });
          formControl.setValue(parsedDates);
        } else {
          formControl.setValue(data[key]);
        }

        if (this.isViewMode) {
          formControl.disable(); // Disable the control AFTER setting the value
        }
      }
    });
  }
  
  //populatedefualt fields 
  populateDefaultFields(defaultFields: any[]) {
    if (!Array.isArray(defaultFields)) return;
    defaultFields.forEach(item => {
      if (item && typeof item === 'object') {
        // Handle API structure: { defaultField, defaultValue }
        if ('defaultField' in item && 'defaultValue' in item) {
          const fieldName = item.defaultField;
          const defaultValue = item.defaultValue;
          // Find field by trimmed name to handle trailing spaces
          const field = this.fields.find(f => f.fieldName.trim() === fieldName.trim());
          const actualFieldName = field?.fieldName || fieldName;
          const formControl = this.form.get(actualFieldName);
          if (formControl && defaultValue !== null && defaultValue !== undefined) {
            const wasDisabled = formControl.disabled;
            if (wasDisabled) formControl.enable({ emitEvent: false });
            if (field && field.type === 'date' && typeof defaultValue === 'string') {
              const parsedDate = new Date(defaultValue);
              if (!isNaN(parsedDate.getTime())) {
                const dateOnly = parsedDate.toISOString().split('T')[0];
                formControl.setValue(dateOnly);
              }
            } else if (field && field.type === 'boolean') {
              const boolValue = defaultValue === true || defaultValue === 'true' || defaultValue === 1 || defaultValue === '1';
              formControl.setValue(boolValue);
            } else if (field && (field.type === 'int' || field.type === 'double')) {
              const numValue = parseFloat(defaultValue);
              if (!isNaN(numValue)) {
                formControl.setValue(numValue);
              }
            } else {
              formControl.setValue(defaultValue);
            }
            if (wasDisabled) formControl.disable({ emitEvent: false });
          }
        } else {
          // Fallback: handle { fieldName: value } structure
          Object.keys(item).forEach(fieldName => {
            const defaultValue = item[fieldName];
            // Find field by trimmed name to handle trailing spaces
            const field = this.fields.find(f => f.fieldName.trim() === fieldName.trim());
            const actualFieldName = field?.fieldName || fieldName;
            const formControl = this.form.get(actualFieldName);
            if (formControl && defaultValue !== null && defaultValue !== undefined) {
              const wasDisabled = formControl.disabled;
              if (wasDisabled) formControl.enable({ emitEvent: false });
              if (field && field.type === 'date' && typeof defaultValue === 'string') {
                const parsedDate = new Date(defaultValue);
                if (!isNaN(parsedDate.getTime())) {
                  const dateOnly = parsedDate.toISOString().split('T')[0];
                  formControl.setValue(dateOnly);
                }
              } else if (field && field.type === 'boolean') {
                const boolValue = defaultValue === true || defaultValue === 'true' || defaultValue === 1 || defaultValue === '1';
                formControl.setValue(boolValue);
              } else if (field && (field.type === 'int' || field.type === 'double')) {
                const numValue = parseFloat(defaultValue);
                if (!isNaN(numValue)) {
                  formControl.setValue(numValue);
                }
              } else {
                formControl.setValue(defaultValue);
              }
              if (wasDisabled) formControl.disable({ emitEvent: false });
            }
          });
        }
      }
    });
  }



  goBack() {
    const id = this.form.get("ID")?.value;
    const tableNameToUse = this.tableName || this.screenName;
    const tablesApiId = this.tableUtilsService.extractTablesApiId(tableNameToUse);
    const apiUrl = `${environment.baseURL}/api/tables/${tablesApiId}/records/${id}/force-unlock`;

    this.http.delete(apiUrl, { withCredentials: true }).subscribe({
      next: () => {
        // Record unlocked successfully
      },
      error: (error) => {
        this.cleanupForm();
      },
      complete: () => {
        this.cleanupForm();
      }
    });
  }

  private cleanupForm() {
    // First remove all controls from the form except ID
    Object.keys(this.form.controls).forEach(key => {
      if (key !== 'ID') {
        this.form.removeControl(key);
      }
    });

    // Reset the form
    this.form.reset();

    // Clear all fields and state
    this.fields = [];
    this.showInitialInput = true;
    this.isViewMode = false;
    this.isAuth = true;
    this.submissionSuccess = false;
    this.validationResult = null;
    this.showValidation = false; // Reset validation state
    this.setFormReadonly(false);


  }

  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  toggleViewMode() {
    this.isRowView = !this.isRowView;
  }

  // Form Actions Component Event Handlers
  onSubmissionSuccess(success: boolean) {
    this.submissionSuccess = success;
  }

  onErrorMessageChange(message: string) {
    this.errorMessage = message;
  }

  onIsLoadingChange(loading: boolean) {
    this.isLoading = loading;
  }

  onShowSuccessPopupChange(show: boolean) {
    this.showSuccessPopup = show;
  }

  onSuccessMessageChange(message: string) {
    this.successMessage = message;
  }

  onValidationResultChange(result: any) {
    this.validationResult = result;
  }

  onGoBackRequested() {
    this.goBack();
  }

  onSetFormReadonly(readonly: boolean) {
    this.setFormReadonly(readonly);
  }

  onPopulateForm(data: any) {
    this.populateForm(data);
    this.populateSubScreenForms(data);
  }

  /**
   * Populates subscreen forms with data from validation/submission responses
   * @param data - The response data containing subscreen field values
   */
  populateSubScreenForms(data: any): void {
    if (!this.hasSubScreens || !this.subScreens || this.subScreens.length === 0) {
      return;
    }

    console.log('🔍 Starting subscreen population with data:', data);

    this.subScreens.forEach(subScreenId => {
      const subScreenForm = this.subScreenForms[subScreenId];
      if (!subScreenForm) {
        console.warn(`⚠️ No form found for subscreen: ${subScreenId}`);
        return;
      }

      const subScreenMetadata = this.subScreensMetadata.find(meta => meta.ID === subScreenId);
      if (!subScreenMetadata || !subScreenMetadata.fieldName) {
        console.warn(`⚠️ No metadata found for subscreen: ${subScreenId}`);
        return;
      }

      console.log(`📋 Processing subscreen: ${subScreenId}`);
      console.log(`📋 Subscreen metadata fields:`, subScreenMetadata.fieldName);

      // Create field mapping to handle name mismatches (with/without trailing spaces)
      const fieldMapping = new Map<string, string>();
      const groupMapping = new Map<string, string>();

      subScreenMetadata.fieldName.forEach((field: any) => {
        if (field.fieldName && field.fieldName.toUpperCase() !== 'ID') {
          const trimmedFieldName = field.fieldName.trim();

          // Map API response field names to metadata field names
          Object.keys(data).forEach(apiKey => {
            const trimmedApiKey = apiKey.trim();
            if (trimmedApiKey === trimmedFieldName) {
              fieldMapping.set(apiKey, field.fieldName);
              console.log(`🔗 Field mapping: ${apiKey} -> ${field.fieldName}`);
            }
          });

          // Handle grouped fields
          if (field.Group) {
            const trimmedGroupName = field.Group.trim();
            Object.keys(data).forEach(apiKey => {
              const trimmedApiKey = apiKey.trim();
              if (trimmedApiKey === trimmedGroupName) {
                groupMapping.set(apiKey, field.Group);
                console.log(`🔗 Group mapping: ${apiKey} -> ${field.Group}`);
              }
            });
          }
        }
      });

      // Populate subscreen form with mapped data
      this.populateSubScreenWithMappedData(subScreenId, subScreenForm, subScreenMetadata, data, fieldMapping, groupMapping);
    });
  }

  /**
   * Helper method to populate a specific subscreen with mapped data
   */
  private populateSubScreenWithMappedData(
    subScreenId: string,
    subScreenForm: FormGroup,
    subScreenMetadata: any,
    data: any,
    fieldMapping: Map<string, string>,
    groupMapping: Map<string, string>
  ): void {
    console.log(`🎯 Populating subscreen ${subScreenId}`);

    // Handle grouped fields first (like documents)
    groupMapping.forEach((metadataGroupName, apiGroupName) => {
      if (data[apiGroupName] && Array.isArray(data[apiGroupName])) {
        console.log(`📦 Processing group: ${apiGroupName} -> ${metadataGroupName}`);

        const groupArray = subScreenForm.get(metadataGroupName) as FormArray;
        if (groupArray) {
          groupArray.clear();

          data[apiGroupName].forEach((groupData: any) => {
            console.log(`📦 Adding group data:`, groupData);
            const groupForm = this.createSubScreenGroup(subScreenMetadata, metadataGroupName) as FormGroup;
            groupForm.patchValue(groupData);
            groupArray.push(groupForm);
          });
        }
      }
    });

    // Handle regular fields and multi-fields
    fieldMapping.forEach((metadataFieldName, apiFieldName) => {
      if (data.hasOwnProperty(apiFieldName)) {
        const formControl = subScreenForm.get(metadataFieldName);
        const fieldValue = data[apiFieldName];

        console.log(`🔧 Processing field: ${apiFieldName} -> ${metadataFieldName}`, fieldValue);

        if (formControl instanceof FormArray && Array.isArray(fieldValue)) {
          // Handle multi-fields
          const formArray = formControl as FormArray;
          formArray.clear();

          const fieldMeta = subScreenMetadata.fieldName.find((f: any) => f.fieldName === metadataFieldName);
          if (fieldMeta) {
            fieldValue.forEach((item: any) => {
              const multiGroup = this.createMultiField(fieldMeta);
              if (typeof item === 'object' && !Array.isArray(item)) {
                multiGroup.patchValue(item);
              } else {
                multiGroup.patchValue({ [metadataFieldName]: item });
              }
              formArray.push(multiGroup);
            });
          }
        } else if (formControl) {
          // Handle regular fields
          const fieldMeta = subScreenMetadata.fieldName.find((f: any) => f.fieldName === metadataFieldName);
          if (fieldMeta && fieldMeta.type === 'date' && typeof fieldValue === 'string') {
            const parsedDate = new Date(fieldValue);
            const dateOnly = parsedDate.toISOString().split('T')[0];
            if (!isNaN(parsedDate.getTime())) {
              formControl.setValue(dateOnly);
            } else {
              formControl.setValue(null);
            }
          } else {
            formControl.setValue(fieldValue);
          }
        }
      }
    });

    console.log(`✅ Completed populating subscreen ${subScreenId}`);
  }

  onPopulateDefaultFields(fields: any[]) {
    this.populateDefaultFields(fields);
  }

  onSetViewMode(viewMode: boolean) {
    this.isViewMode = viewMode;
  }

  // Form Header Delegation Methods
  onFormSubmit() {
    this.formActions.onSubmit();
  }

  onFormValidate() {
    this.formActions.validateRecord();
  }

  onFormAuthorize() {
    this.formActions.authorizeRecord();
  }

  onFormReject() {
    this.formActions.onRejectRecord();
  }

  onFormDelete() {
    this.formActions.onDeleteRecord();
  }




  cloneGroup(groupName: string, index: number): void {
    const groupArray = this.getGroupArray(groupName);
    const groupToClone = groupArray.at(index) as FormGroup;

    // Step 1: Add a new empty group using your existing method
    this.addGroup(groupName, index);

    // Step 2: Get the newly inserted group
    const clonedGroup = groupArray.at(index + 1) as FormGroup;

    // Step 3: Copy values (deep clone including nested FormArrays)
    Object.keys(groupToClone.controls).forEach(key => {
      const control = groupToClone.get(key);
      const clonedControl = clonedGroup.get(key);

      if (control instanceof FormArray && clonedControl instanceof FormArray) {
        clonedControl.clear();
        control.controls.forEach(c => {
          const clonedSubGroup = this.fb.group({});
          Object.keys((c as FormGroup).controls).forEach(subKey => {
            clonedSubGroup.addControl(subKey, this.fb.control((c as FormGroup).get(subKey)?.value));
          });
          clonedControl.push(clonedSubGroup);
        });
      } else {
        clonedControl?.setValue(control?.value);
      }
    });
  }



  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  onValidationChange(showValidation: boolean): void {
    this.showValidation = showValidation;
  }


  isIdValid(): boolean {
    const idValue = this.form.get('ID')?.value;
    return idValue && idValue.trim() !== '';
  }

  // Handle field value changes from child components
  onFieldValueChange(_event: {fieldName: string, value: any}): void {
    // Child component already handles form control update
    // This method can be used for additional logic if needed
  }

}

